package main

import (
	"log"
	mux "gitlink.org.cn/nanakura/gnet-mux"
	// 示例：使用 goccy/go-json 作为第三方JSON库
	// "github.com/goccy/go-json"
	// 或者使用 json-iterator
	// jsoniter "github.com/json-iterator/go"
)

func main() {
	// 方式1: 使用默认的 encoding/json
	r1 := mux.New()
	
	// 方式2: 使用自定义JSON编解码器
	// 注意：这里注释掉了第三方库的使用，因为需要先安装依赖
	/*
	r2 := mux.NewWithConfig(mux.Config{
		JSONEncoder: json.Marshal,   // 使用 goccy/go-json
		JSONDecoder: json.Unmarshal, // 使用 goccy/go-json
	})
	*/
	
	// 方式3: 使用 json-iterator
	/*
	var jsonAPI = jsoniter.ConfigCompatibleWithStandardLibrary
	r3 := mux.NewWithConfig(mux.Config{
		JSONEncoder: jsonAPI.Marshal,
		JSONDecoder: jsonAPI.Unmarshal,
	})
	*/
	
	// 使用默认配置的示例
	r := r1
	
	// 测试JSON响应
	r.GET("/json", func(c *mux.Context) {
		c.JSON(mux.H{
			"message": "Hello from custom JSON encoder!",
			"data": map[string]interface{}{
				"id":   123,
				"name": "Test User",
				"tags": []string{"go", "gnet", "json"},
			},
		})
	})
	
	// 测试JSON绑定
	type User struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
		Age  int    `json:"age"`
	}
	
	r.POST("/user", func(c *mux.Context) {
		var user User
		if err := c.ShouldBindJSON(&user); err != nil {
			c.JSON(mux.H{"error": err.Error()})
			return
		}
		
		c.JSON(mux.H{
			"message": "User created successfully",
			"user":    user,
		})
	})
	
	// 启动服务器
	log.Println("Starting server with custom JSON configuration on :8081")
	if err := r.Run(":8081"); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}