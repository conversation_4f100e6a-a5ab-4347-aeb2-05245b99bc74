package mux

import (
	"bytes"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"reflect"
	"strconv"
	"strings"

	"github.com/go-playground/validator/v10"
)

// Binding interface defines the binding behavior
type Binding interface {
	Name() string
	Bind(*http.Request, interface{}) error
}

// BindingBody interface extends Binding for body-based bindings
type BindingBody interface {
	Binding
	BindBody([]byte, interface{}) error
}

// Validator instance
var Validator = validator.New()

func init() {
	// Configure validator to use 'binding' tag instead of 'validate'
	Validator.SetTagName("binding")
}

// Binding instances
var (
	JSON          = jsonBinding{}
	XML           = xmlBinding{}
	Form          = formBinding{}
	Query         = queryBinding{}
	FormPost      = formPostBinding{}
	FormMultipart = formMultipartBinding{}
)

// JSON binding
type jsonBinding struct{}

func (jsonBinding) Name() string {
	return "json"
}

func (jsonBinding) Bind(req *http.Request, obj interface{}) error {
	body, err := io.ReadAll(req.Body)
	if err != nil {
		return err
	}
	if err := json.Unmarshal(body, obj); err != nil {
		return err
	}
	return Validator.Struct(obj)
}

func (jsonBinding) BindBody(body []byte, obj interface{}) error {
	if err := json.Unmarshal(body, obj); err != nil {
		return err
	}
	return Validator.Struct(obj)
}

// XML binding
type xmlBinding struct{}

func (xmlBinding) Name() string {
	return "xml"
}

func (xmlBinding) Bind(req *http.Request, obj interface{}) error {
	body, err := io.ReadAll(req.Body)
	if err != nil {
		return err
	}
	if err := xml.Unmarshal(body, obj); err != nil {
		return err
	}
	return Validator.Struct(obj)
}

func (xmlBinding) BindBody(body []byte, obj interface{}) error {
	if err := xml.Unmarshal(body, obj); err != nil {
		return err
	}
	return Validator.Struct(obj)
}

// Form binding
type formBinding struct{}

func (formBinding) Name() string {
	return "form"
}

func (formBinding) Bind(req *http.Request, obj interface{}) error {
	if err := req.ParseForm(); err != nil {
		return err
	}
	return mapForm(obj, req.Form)
}

// Query binding
type queryBinding struct{}

func (queryBinding) Name() string {
	return "query"
}

func (queryBinding) Bind(req *http.Request, obj interface{}) error {
	return mapForm(obj, req.URL.Query())
}

// FormPost binding
type formPostBinding struct{}

func (formPostBinding) Name() string {
	return "form-data"
}

func (formPostBinding) Bind(req *http.Request, obj interface{}) error {
	if err := req.ParseForm(); err != nil {
		return err
	}
	return mapForm(obj, req.PostForm)
}

// FormMultipart binding
type formMultipartBinding struct{}

func (formMultipartBinding) Name() string {
	return "multipart/form-data"
}

func (formMultipartBinding) Bind(req *http.Request, obj interface{}) error {
	if err := req.ParseMultipartForm(32 << 20); err != nil {
		return err
	}
	return mapForm(obj, req.MultipartForm.Value)
}

// mapForm maps form values to struct
func mapForm(ptr interface{}, form map[string][]string) error {
	typ := reflect.TypeOf(ptr).Elem()
	val := reflect.ValueOf(ptr).Elem()

	for i := 0; i < typ.NumField(); i++ {
		typeField := typ.Field(i)
		structField := val.Field(i)
		if !structField.CanSet() {
			continue
		}

		structFieldKind := structField.Kind()
		inputFieldName := typeField.Tag.Get("form")
		if inputFieldName == "" {
			inputFieldName = strings.ToLower(typeField.Name)
		}
		if inputFieldName == "-" {
			continue
		}

		inputValue, exists := form[inputFieldName]
		if !exists {
			continue
		}

		numElems := len(inputValue)
		if structFieldKind == reflect.Slice && numElems > 0 {
			sliceOf := structField.Type().Elem().Kind()
			slice := reflect.MakeSlice(structField.Type(), numElems, numElems)
			for j := 0; j < numElems; j++ {
				if err := setWithProperType(sliceOf, inputValue[j], slice.Index(j)); err != nil {
					return err
				}
			}
			val.Field(i).Set(slice)
		} else if numElems > 0 {
			if err := setWithProperType(structFieldKind, inputValue[0], structField); err != nil {
				return err
			}
		}
	}
	return nil
}

// setWithProperType sets value with proper type conversion
func setWithProperType(valueKind reflect.Kind, val string, structField reflect.Value) error {
	switch valueKind {
	case reflect.Int:
		return setIntField(val, 0, structField)
	case reflect.Int8:
		return setIntField(val, 8, structField)
	case reflect.Int16:
		return setIntField(val, 16, structField)
	case reflect.Int32:
		return setIntField(val, 32, structField)
	case reflect.Int64:
		return setIntField(val, 64, structField)
	case reflect.Uint:
		return setUintField(val, 0, structField)
	case reflect.Uint8:
		return setUintField(val, 8, structField)
	case reflect.Uint16:
		return setUintField(val, 16, structField)
	case reflect.Uint32:
		return setUintField(val, 32, structField)
	case reflect.Uint64:
		return setUintField(val, 64, structField)
	case reflect.Bool:
		return setBoolField(val, structField)
	case reflect.Float32:
		return setFloatField(val, 32, structField)
	case reflect.Float64:
		return setFloatField(val, 64, structField)
	case reflect.String:
		structField.SetString(val)
	default:
		return fmt.Errorf("unknown type: %s", valueKind)
	}
	return nil
}

func setIntField(val string, bitSize int, field reflect.Value) error {
	if val == "" {
		val = "0"
	}
	intVal, err := strconv.ParseInt(val, 10, bitSize)
	if err == nil {
		field.SetInt(intVal)
	}
	return err
}

func setUintField(val string, bitSize int, field reflect.Value) error {
	if val == "" {
		val = "0"
	}
	uintVal, err := strconv.ParseUint(val, 10, bitSize)
	if err == nil {
		field.SetUint(uintVal)
	}
	return err
}

func setBoolField(val string, field reflect.Value) error {
	if val == "" {
		val = "false"
	}
	boolVal, err := strconv.ParseBool(val)
	if err == nil {
		field.SetBool(boolVal)
	}
	return err
}

func setFloatField(val string, bitSize int, field reflect.Value) error {
	if val == "" {
		val = "0.0"
	}
	floatVal, err := strconv.ParseFloat(val, bitSize)
	if err == nil {
		field.SetFloat(floatVal)
	}
	return err
}

// validate validates the struct using the validator
func validate(obj interface{}) error {
	if Validator == nil {
		return nil
	}
	return Validator.Struct(obj)
}

// Context binding methods

// ShouldBind binds the request to obj based on content type
func (c *Context) ShouldBind(obj interface{}) error {
	b := DefaultBinding(c.request.Method, c.GetHeader("Content-Type"))
	return c.ShouldBindWith(obj, b)
}

// ShouldBindJSON binds the request body as JSON
func (c *Context) ShouldBindJSON(obj interface{}) error {
	return c.ShouldBindWith(obj, JSON)
}

// ShouldBindXML binds the request body as XML
func (c *Context) ShouldBindXML(obj interface{}) error {
	return c.ShouldBindWith(obj, XML)
}

// ShouldBindQuery binds the query parameters
func (c *Context) ShouldBindQuery(obj interface{}) error {
	return c.ShouldBindWith(obj, Query)
}

// ShouldBindYAML is not implemented yet
func (c *Context) ShouldBindYAML(obj interface{}) error {
	return fmt.Errorf("YAML binding not implemented")
}

// ShouldBindWith binds the request using the specified binding
func (c *Context) ShouldBindWith(obj interface{}, b Binding) error {
	if err := b.Bind(c.request, obj); err != nil {
		return err
	}
	return validate(obj)
}

// ShouldBindBodyWith binds the request body and stores it in context for reuse
func (c *Context) ShouldBindBodyWith(obj interface{}, bb BindingBody) error {
	body, err := c.getBody(bb)
	if err != nil {
		return err
	}
	if err := bb.BindBody(body, obj); err != nil {
		return err
	}
	return validate(obj)
}

// getBody gets the request body and caches it in context
func (c *Context) getBody(bb BindingBody) ([]byte, error) {
	key := "__body_" + bb.Name()
	if cached, exists := c.Get(key); exists {
		return cached.([]byte), nil
	}

	body, err := io.ReadAll(c.request.Body)
	if err != nil {
		return nil, err
	}

	// Reset the body for potential reuse
	c.request.Body = io.NopCloser(bytes.NewBuffer(body))
	c.Set(key, body)
	return body, nil
}

// Must bind methods (abort on error)

// Bind binds the request and aborts with 400 on error
func (c *Context) Bind(obj interface{}) error {
	b := DefaultBinding(c.request.Method, c.GetHeader("Content-Type"))
	return c.MustBindWith(obj, b)
}

// BindJSON binds JSON and aborts with 400 on error
func (c *Context) BindJSON(obj interface{}) error {
	return c.MustBindWith(obj, JSON)
}

// BindXML binds XML and aborts with 400 on error
func (c *Context) BindXML(obj interface{}) error {
	return c.MustBindWith(obj, XML)
}

// BindQuery binds query parameters and aborts with 400 on error
func (c *Context) BindQuery(obj interface{}) error {
	return c.MustBindWith(obj, Query)
}

// BindYAML is not implemented yet
func (c *Context) BindYAML(obj interface{}) error {
	return fmt.Errorf("YAML binding not implemented")
}

// MustBindWith binds using the specified binding and aborts on error
func (c *Context) MustBindWith(obj interface{}, b Binding) error {
	if err := c.ShouldBindWith(obj, b); err != nil {
		c.AbortWithError(400, err)
		return err
	}
	return nil
}

// AbortWithError aborts with error
func (c *Context) AbortWithError(code int, err error) {
	c.AbortWithStatusJSON(code, H{"error": err.Error()})
}

// DefaultBinding returns the appropriate binding based on method and content type
func DefaultBinding(method, contentType string) Binding {
	if method == "GET" {
		return Form
	}

	switch contentType {
	case "application/json":
		return JSON
	case "application/xml", "text/xml":
		return XML
	case "application/x-www-form-urlencoded":
		return FormPost
	case "multipart/form-data":
		return FormMultipart
	default:
		return Form
	}
}