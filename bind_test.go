package mux

import (
	"net/http"
	"net/url"
	"strings"
	"testing"
)

// Test structs
type TestUser struct {
	Name     string `json:"name" form:"name" binding:"required,min=2"`
	Email    string `json:"email" form:"email" binding:"required,email"`
	Age      int    `json:"age" form:"age" binding:"required,min=1,max=120"`
	Password string `json:"password" form:"password" binding:"required,min=6"`
}

type TestQuery struct {
	Q     string `form:"q" binding:"required"`
	Limit int    `form:"limit" binding:"min=1,max=100"`
	Page  int    `form:"page" binding:"min=1"`
}

func createTestRequest(method, url, contentType, body string) *http.Request {
	req, _ := http.NewRequest(method, url, strings.NewReader(body))
	if contentType != "" {
		req.Header.Set("Content-Type", contentType)
	}
	return req
}

func createTestContext(req *http.Request) *Context {
	engine := New()
	return &Context{
		request: req,
		response: &Response{
			headers:    make(map[string]string),
			statusCode: 200,
		},
		params: make(map[string]string),
		keys:   make(map[string]interface{}),
		engine: engine,
	}
}

func TestShouldBindJSON(t *testing.T) {
	// Valid JSON
	jsonData := `{"name":"John Doe","email":"<EMAIL>","age":25,"password":"secret123"}`
	req := createTestRequest("POST", "/test", "application/json", jsonData)
	c := createTestContext(req)

	var user TestUser
	err := c.ShouldBindJSON(&user)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if user.Name != "John Doe" {
		t.Errorf("Expected name 'John Doe', got '%s'", user.Name)
	}
	if user.Email != "<EMAIL>" {
		t.Errorf("Expected email '<EMAIL>', got '%s'", user.Email)
	}
	if user.Age != 25 {
		t.Errorf("Expected age 25, got %d", user.Age)
	}
}

func TestShouldBindJSONValidationError(t *testing.T) {
	// Invalid JSON - missing required field and invalid email
	jsonData := `{"name":"J","email":"invalid-email","age":25,"password":"123"}`
	req := createTestRequest("POST", "/test", "application/json", jsonData)
	c := createTestContext(req)

	var user TestUser
	err := c.ShouldBindJSON(&user)
	if err == nil {
		t.Error("Expected validation error, got nil")
	}
}

func TestShouldBindXML(t *testing.T) {
	xmlData := `<TestUser><Name>John Doe</Name><Email><EMAIL></Email><Age>25</Age><Password>secret123</Password></TestUser>`
	req := createTestRequest("POST", "/test", "application/xml", xmlData)
	c := createTestContext(req)

	var user TestUser
	err := c.ShouldBindXML(&user)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if user.Name != "John Doe" {
		t.Errorf("Expected name 'John Doe', got '%s'", user.Name)
	}
}

func TestShouldBindQuery(t *testing.T) {
	req := createTestRequest("GET", "/test?q=golang&limit=10&page=1", "", "")
	c := createTestContext(req)

	var query TestQuery
	err := c.ShouldBindQuery(&query)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if query.Q != "golang" {
		t.Errorf("Expected q 'golang', got '%s'", query.Q)
	}
	if query.Limit != 10 {
		t.Errorf("Expected limit 10, got %d", query.Limit)
	}
	if query.Page != 1 {
		t.Errorf("Expected page 1, got %d", query.Page)
	}
}

func TestShouldBindForm(t *testing.T) {
	formData := url.Values{}
	formData.Set("name", "John Doe")
	formData.Set("email", "<EMAIL>")
	formData.Set("age", "25")
	formData.Set("password", "secret123")

	req := createTestRequest("POST", "/test", "application/x-www-form-urlencoded", formData.Encode())
	c := createTestContext(req)

	var user TestUser
	err := c.ShouldBind(&user)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if user.Name != "John Doe" {
		t.Errorf("Expected name 'John Doe', got '%s'", user.Name)
	}
}

func TestShouldBindBodyWith(t *testing.T) {
	jsonData := `{"name":"John Doe","email":"<EMAIL>","age":25,"password":"secret123"}`
	req := createTestRequest("POST", "/test", "application/json", jsonData)
	c := createTestContext(req)

	// First bind
	var user1 TestUser
	err := c.ShouldBindBodyWith(&user1, JSON)
	if err != nil {
		t.Errorf("Expected no error on first bind, got %v", err)
	}

	// Second bind - should work because body is cached
	var user2 TestUser
	err = c.ShouldBindBodyWith(&user2, JSON)
	if err != nil {
		t.Errorf("Expected no error on second bind, got %v", err)
	}

	if user1.Name != user2.Name {
		t.Errorf("Expected same name in both binds, got '%s' and '%s'", user1.Name, user2.Name)
	}
}

func TestDefaultBinding(t *testing.T) {
	tests := []struct {
		method      string
		contentType string
		expected    string
	}{
		{"GET", "", "form"},
		{"POST", "application/json", "json"},
		{"POST", "application/xml", "xml"},
		{"POST", "text/xml", "xml"},
		{"POST", "application/x-www-form-urlencoded", "form-data"},
		{"POST", "multipart/form-data", "multipart/form-data"},
		{"POST", "unknown", "form"},
	}

	for _, test := range tests {
		binding := DefaultBinding(test.method, test.contentType)
		if binding.Name() != test.expected {
			t.Errorf("For method %s and content-type %s, expected %s, got %s",
				test.method, test.contentType, test.expected, binding.Name())
		}
	}
}

func TestBindingInterfaces(t *testing.T) {
	// Test that all bindings implement the Binding interface
	bindings := []Binding{JSON, XML, Form, Query, FormPost, FormMultipart}
	for _, b := range bindings {
		if b.Name() == "" {
			t.Errorf("Binding %T should have a non-empty name", b)
		}
	}

	// Test that body bindings implement BindingBody interface
	bodyBindings := []BindingBody{JSON, XML}
	for _, bb := range bodyBindings {
		if bb.Name() == "" {
			t.Errorf("BindingBody %T should have a non-empty name", bb)
		}
	}
}

func TestValidationTags(t *testing.T) {
	// Test various validation scenarios
	tests := []struct {
		name        string
		jsonData    string
		shouldError bool
		description string
	}{
		{
			"valid_data",
			`{"name":"John Doe","email":"<EMAIL>","age":25,"password":"secret123"}`,
			false,
			"Valid user data",
		},
		{
			"missing_required",
			`{"email":"<EMAIL>","age":25,"password":"secret123"}`,
			true,
			"Missing required name field",
		},
		{
			"invalid_email",
			`{"name":"John Doe","email":"invalid-email","age":25,"password":"secret123"}`,
			true,
			"Invalid email format",
		},
		{
			"short_name",
			`{"name":"J","email":"<EMAIL>","age":25,"password":"secret123"}`,
			true,
			"Name too short (min=2)",
		},
		{
			"short_password",
			`{"name":"John Doe","email":"<EMAIL>","age":25,"password":"123"}`,
			true,
			"Password too short (min=6)",
		},
		{
			"invalid_age_low",
			`{"name":"John Doe","email":"<EMAIL>","age":0,"password":"secret123"}`,
			true,
			"Age too low (min=1)",
		},
		{
			"invalid_age_high",
			`{"name":"John Doe","email":"<EMAIL>","age":150,"password":"secret123"}`,
			true,
			"Age too high (max=120)",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			req := createTestRequest("POST", "/test", "application/json", test.jsonData)
			c := createTestContext(req)

			var user TestUser
			err := c.ShouldBindJSON(&user)

			if test.shouldError && err == nil {
				t.Errorf("%s: Expected error but got none", test.description)
			} else if !test.shouldError && err != nil {
				t.Errorf("%s: Expected no error but got: %v", test.description, err)
			}
		})
	}
}