package main

import (
	"fmt"
	"log"

	mux "gitlink.org.cn/nanakura/gnet-mux"
)

// Mock handlers for demonstration (following Gin example exactly)
func loginEndpoint(c *mux.Context) {
	c.JSON(mux.H{
		"message": "Login endpoint",
		"path":    c.Request().URL.Path,
	})
}

func submitEndpoint(c *mux.Context) {
	c.JSON(mux.H{
		"message": "Submit endpoint", 
		"path":    c.Request().URL.Path,
	})
}

func readEndpoint(c *mux.Context) {
	c.JSON(mux.H{
		"message": "Read endpoint",
		"path":    c.Request().URL.Path,
	})
}

func main() {
	router := mux.Default()

	// Simple group: v1 (following Gin example exactly)
	{
		v1 := router.Group("/v1")
		v1.POST("/login", loginEndpoint)
		v1.POST("/submit", submitEndpoint)
		v1.POST("/read", readEndpoint)
	}

	// Simple group: v2 (following Gin example exactly)
	{
		v2 := router.Group("/v2")
		v2.POST("/login", loginEndpoint)
		v2.POST("/submit", submitEndpoint)
		v2.POST("/read", readEndpoint)
	}

	fmt.Println("Server starting on :8080")
	fmt.Println("Try these endpoints:")
	fmt.Println("  POST /v1/login")
	fmt.Println("  POST /v1/submit")
	fmt.Println("  POST /v1/read")
	fmt.Println("  POST /v2/login")
	fmt.Println("  POST /v2/submit")
	fmt.Println("  POST /v2/read")
	fmt.Println("")
	fmt.Println("Example curl commands:")
	fmt.Println("  curl -X POST http://localhost:8080/v1/login")
	fmt.Println("  curl -X POST http://localhost:8080/v2/submit")

	log.Fatal(router.Run(":8080"))
}
