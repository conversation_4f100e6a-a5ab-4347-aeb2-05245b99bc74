package main

import (
	"fmt"
	"log"
	"net/http"
	"os"

	mux "gitlink.org.cn/nanakura/gnet-mux"
)

func main() {
	router := mux.Default()

	// Create demo directories and files
	setupSimpleDemoFiles()

	// Static file serving (following Gin example exactly)
	router.Static("/assets", "./assets")
	router.StaticFS("/more_static", http.Dir("my_file_system"))
	router.StaticFile("/favicon.ico", "./resources/favicon.ico")

	// Simple home page
	router.GET("/", func(c *mux.Context) {
		c.HTML(`
<!DOCTYPE html>
<html>
<head>
    <title>Static File Demo</title>
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
</head>
<body>
    <h1>Static File Serving Demo</h1>
    <p>This demonstrates the Gin-compatible static file serving.</p>
    <ul>
        <li><a href="/assets/test.txt">Test file from /assets</a></li>
        <li><a href="/more_static/config.txt">Config from /more_static</a></li>
        <li><a href="/favicon.ico">Favicon</a></li>
    </ul>
</body>
</html>
		`)
	})

	fmt.Println("Server starting on :8080")
	fmt.Println("Static file routes:")
	fmt.Println("  /assets -> ./assets")
	fmt.Println("  /more_static -> ./my_file_system")
	fmt.Println("  /favicon.ico -> ./resources/favicon.ico")

	// Listen and serve on 0.0.0.0:8080 (following Gin example exactly)
	log.Fatal(router.Run(":8080"))
}

func setupSimpleDemoFiles() {
	// Create directories
	os.MkdirAll("assets", 0755)
	os.MkdirAll("my_file_system", 0755)
	os.MkdirAll("resources", 0755)

	// Create test files
	os.WriteFile("assets/test.txt", []byte("Hello from assets directory!"), 0644)
	os.WriteFile("my_file_system/config.txt", []byte("Configuration file content"), 0644)
	
	// Create a simple favicon
	faviconData := []byte{
		0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x10, 0x10, 0x00, 0x00, 0x01, 0x00,
		0x04, 0x00, 0x28, 0x01, 0x00, 0x00, 0x16, 0x00, 0x00, 0x00,
	}
	os.WriteFile("resources/favicon.ico", faviconData, 0644)

	fmt.Println("Demo files created!")
}
